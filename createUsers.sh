#!/bin/bash

password=1357qwer@A

# 创建系统管理员账户
useradd sysadmin
echo "设置系统管理员密码"
echo sysadmin:$PASSWORD | chpasswd

# 创建安全管理员账户
useradd secadmin
echo "设置安全管理员密码"
echo secadmin:$PASSWORD | chpasswd

# 创建审计管理员账户
useradd auditadmin
echo "设置审计管理员密码"
echo auditadmin:$PASSWORD | chpasswd

# 创建用户组（如果需要）
groupadd sysgroup
groupadd secgroup
groupadd auditgroup

# 将用户添加到用户组
usermod -G sysgroup sysadmin
usermod -G secgroup secadmin
usermod -G auditgroup auditadmin

# 配置sudo权限
echo "配置sudo权限"
#visudo

# 在这里，你可以添加如下行到sudoers文件中，以限制用户的sudo权限
# %sysgroup ALL=(ALL) ALL
# %secgroup ALL=(ALL) ALL
# %auditgroup ALL=(ALL) NOPASSWD: /usr/sbin/auditctl

# 设置文件和目录权限
echo "设置文件和目录权限"
# 例如，给予系统管理员对系统配置文件的访问权限
#chown -R sysadmin:sysgroup /etc/sysconfig
#chmod 700 /etc/sysconfig

# 给予安全管理员对安全相关文件的访问权限
#chown -R secadmin:secgroup /etc/security
#chmod 700 /etc/security

# 给予审计管理员对审计日志的访问权限
#chown -R auditadmin:auditgroup /var/log/audit
#chmod 700 /var/log/audit

echo "三权分立账户创建完毕"
