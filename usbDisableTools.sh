#!/bin/bash
blacklistfilename="/etc/modprobe.d/blacklist.conf"
enableUsb(){
  sudo rm -f $blacklistfilename
  echo -e "usb已经启用，请重启设备以使设置生效"
}
disableUsb(){
 sudo echo -e "blacklist usb-storage\n">$blacklistfilename
 sudo echo -e "blacklist uas\n">>$blacklistfilename
 sudo echo -e "blacklist usb_storage\n">>$blacklistfilename
 echo -e "usb已经停用，请重启设备以使设置生效"
}
function mainMenu(){
echo '********选项****  '
echo '1.禁用USB(缺省)'
echo '2.启用USB'
echo '3.退出'
read -p "请输入选项: " choise_input
	case $choise_input in
		1)
		disableUsb;;
		2)
		enableUsb;;
		3)
		exit ;;
		*)
		disbaleUsb;;
		esac
}
mainMenu
