#!/bin/bash
#处理网安告警工具脚本
#替换文件中的内容 第一个参数要查找定位的 字符串 第二个参数 需要替换此行的字符串 第3个参数 文件名  第4个参数是否使用了正则表达式 没有第4个参数表示不使用，有表示使用
function replaceFileLine {
  #替换/成 \/
  findwhat="${1//\//\\/}"
  replacewhat="${2//\//\\/}"
  #如果没有使用正则表达式 * 替换成/* ，否则不替换
  if [ $# -lt 4 ] ;then 
    findwhat="${findwhat//\*/\\*}"
    replacewhat="${replacewhat//\*/\\*}"
  fi
  lineno=$(awk "/$findwhat/{ print NR; exit }" $3)
  if [  -n "$lineno" ]; then #存在
    sed -i "${lineno}i $2" $3  #在原行处添加
    lineno=$((lineno+1));
    sed -i "${lineno}d" $3   #删除原行
  else
    lineno=$(awk "/$replacewhat/{ print NR; exit }" $3)
    if [ ! -n "$lineno" ]; then #如果存在要replace的字符串，不做任何事情,否则添加到末尾
        echo -e "\n$2">>$3  #添加到末尾
    fi
  fi
}
function setupvars(){
#define color
color_red='\033[1;31m'
color_no='\033[0m'
color_green='\033[1;32m'
color_yellow='\E[1;33m' #黄
color_blue='\E[1;34m'  #蓝
color_pink='\E[1;35m'      #粉红
ziptmppath="/tmp"
installversion="kemov"   #安装文件版本  kemov  sd
subsvtinstalldir="$HOME/SubSVT-APP-KM"
}
function promptResult()
{
    if  [ $? != 0 ];then
        echo -e  "${color_green}${1}${color_red}出现错误.${color_no}"
    else
        echo -e "${color_green}${1}成功$color_no"
    fi
}
function runAsRoot(){
  if [[ $EUID -ne 0 ]]; then
  	echo -e "请使用root用户运行此脚本!!"
	exit 1
  fi
}
# 检测防火墙类型
function detectFirewall() {
    if which systemctl 1>/dev/null 2>/dev/null && systemctl is-active firewalld 1>/dev/null 2>/dev/null; then
        echo "firewalld"
    elif which ufw 1>/dev/null 2>/dev/null && ufw status | grep -q "Status: active"; then
        echo "ufw"
    else
        echo "none"
    fi
}

function firewallRemovePort()
{
    local firewall_type=$(detectFirewall)

    case "$firewall_type" in
        "firewalld")
            if firewall-cmd --query-port="$1"/tcp 1>/dev/null 2>/dev/null ;then
                echo -e "${color_green}TCP关闭${2}端口...${color_no}"
                firewall-cmd --zone=public --remove-port="$1"/tcp --permanent 1>/dev/null
                promptResult "TCP关闭${2}端口"
                firewall-cmd --reload
            else
                echo -e "${color_green}TCP端口${2}已经关闭,无需处理${color_no}";
            fi
            ;;
        "ufw")
            if ufw status numbered | grep -q "$1/tcp" 1>/dev/null 2>/dev/null; then
                echo -e "${color_green}TCP关闭${2}端口...${color_no}"
                ufw delete allow "$1"/tcp 1>/dev/null 2>/dev/null
                promptResult "TCP关闭${2}端口"
            else
                echo -e "${color_green}TCP端口${2}已经关闭,无需处理${color_no}";
            fi
            ;;
        *)
            echo -e "${color_red}系统没有使用firewall或UFW，关闭${2}TCP端口失败${color_no}"
            ;;
    esac
}
#系统设置加固
function dosystemSettingSecurity(){
#1.默认远程22端口应改为22222，
    filename="/etc/ssh/sshd_config"
    echo -e "${color_green}更改SSH端口-->22222...${color_no}"
    replaceFileLine "(^Port|^#Port)" "Port 22222" $filename
    if [ "$(which semanage 2>/dev/null)" ];then
        semanage port -a -t ssh_port_t -p tcp 22222
    fi
    local firewall_type=$(detectFirewall)
    case "$firewall_type" in
        "firewalld")
            firewall-cmd --zone=public --add-port=22222/tcp --permanent
            removeforward=$(firewall-cmd --list-forward|grep port=2405)
            for removeport in $removeforward ;do
                firewall-cmd --remove-forward-port="$removeport" --permanent
            done
            firewall-cmd --add-forward-port=port=2405:proto=tcp:toport=22222 --permanent
            firewall-cmd --reload
            ;;
        "ufw")
            # UFW允许22222端口
            ufw allow 22222/tcp 1>/dev/null 2>/dev/null
            # UFW不直接支持端口转发，需要手动配置iptables规则
            # 这里添加基本的端口允许，复杂的端口转发需要额外配置
            echo "注意: UFW不直接支持端口转发，如需2405->22222转发请手动配置iptables"
            ;;
        *)
            echo -e "${color_red}系统没有使用firewall或UFW，SSH端口配置可能失败${color_no}"
            ;;
    esac
    promptResult "更改SSH端口为22222"
#2.root不能远程登录
    echo -e "${color_green}设置root不能远程登录SSH...${color_no}"    
    replaceFileLine "(^PermitRootLogin|^#PermitRootLogin)" "PermitRootLogin no" $filename
    promptResult "设置root不能远程登录"
    replaceFileLine "(^#ListenAddress|^ListenAddress)" "ListenAddress 127.0.0.1" ${filename}
    serviceControl restart sshd    
    promptResult "关闭远程SSH登录"
    
#3.设置口令的最长使用期限180天，最短1天，密码过期前28天做提醒密码,最小长度8位。   
    filename="/etc/login.defs" 
    echo -e "${color_green}设置口令的最长使用期限180天，最短1天，密码过期前28天做提醒密码,最小长度8位...${color_no}"    
    replaceFileLine "^PASS_MAX_DAYS" "PASS_MAX_DAYS   180" $filename
    replaceFileLine "^PASS_MIN_DAYS" "PASS_MIN_DAYS   1" $filename
    replaceFileLine "^PASS_MIN_LEN" "PASS_MIN_LEN   8" $filename
    replaceFileLine "^PASS_WARN_AGE" "PASS_WARN_AGE 28" $filename
    promptResult "设置口令的最长使用期限180天，最短1天，密码过期前28天做提醒密码,最小长度8位"
#4.启用登录失败锁定 连续认证失败次数为 5次，锁定该帐户30分钟。
    #a.本地登录限制
    filename="/etc/pam.d/login"    
    echo -e "${color_green}启用本地登录失败5次锁定30分钟...${color_no}"    
    replaceFileLine ".*deny.*=.*unlock_time.*=" "auth required pam_tally2.so deny=5 lock_time=1800 even_deny_root root_unlock_time=1800" $filename  1
    promptResult "启用本地登录失败5次锁定30分钟"
    #b.远程登录限制
    filename="/etc/pam.d/sshd"    
    echo -e "${color_green}启用远程登录失败5次锁定30分钟...${color_no}"    
    replaceFileLine ".*deny.*=.*unlock_time.*=" "auth required pam_tally2.so deny=5 lock_time=1800 even_deny_root root_unlock_time=1800" $filename   1
    promptResult "启用远程登录失败5次锁定30分钟"
#5.设置登录超时控制用户登录会话时间为300秒
    filename="/etc/profile"    
    echo -e "${color_green}设置登录超时控制用户登录会话时间为300秒...${color_no}"   
    replaceFileLine "(^TMOUT|^#TMOUT)" "TMOUT=300" $filename  1
    promptResult "设置登录超时控制用户登录会话时间为300秒"
#6.减少用户输入命令的历史记录长度，保留最新执行的5条命令  
    echo -e "${color_green}减少用户输入命令的历史记录长度，保留最新执行的5条命令...${color_no}"   
    replaceFileLine "HISTSIZE=" "HISTSIZE=5" $filename 
    promptResult "减少用户输入命令的历史记录长度，保留最新执行的5条命令"  
#7.口令复杂度策略 对于采用静态口令认证技术的设备，口令长度至少8位并包括数字、小写字母、大写字母和特殊符号4类中至少3类。   
    filename="/etc/pam.d/system-auth"    
    echo -e "${color_green}启用口令复杂度策略...${color_no}"    
    replaceFileLine ".*password.*requisite.*pam_pwquality.so.*" "password    requisite  pam_pwquality.so minlen=8 dcredit=-1 ucredit=-1 lcredit=-1 ocredit=0 try_first_pass local_users_only retry=3 authtok_type=" $filename 1
    promptResult "启用本地启用口令复杂度策略"
#8.关闭3306端口。
    firewallRemovePort 3306 "3306(mysql)"
    firewallRemovePort 33060 "33060(mysqlx)"
#9.禁用多余的账户sync halt shutdown
    disableLogin
#10.审计日志
    processAuditLog
#11.禁用ping，使用防火墙实现此功能暂时移除系统级屏蔽
    #disablePing
}
#禁用ping
function disablePing(){
    # 禁用 ping：写入 sysctl 并永久保存
    sysctl -w net.ipv4.icmp_echo_ignore_all=1
    grep -q "net.ipv4.icmp_echo_ignore_all" /etc/sysctl.conf || echo "net.ipv4.icmp_echo_ignore_all = 1" >> /etc/sysctl.conf
    echo "✅禁用 ICMP ping成功!"
}
#禁用多余的账户sync halt shutdown
function disableLogin(){
    # 获取所有允许登录的账号（即 /etc/passwd 中拥有 shell 的用户）
    allowed_users=$(awk -F: '$7 !~ /nologin|false/ {print $1}' /etc/passwd)

    # 定义不允许登录的账号列表
    blocked_users=("sync" "halt" "shutdown")

    for user in $allowed_users; do
        if [[ " ${blocked_users[@]} " =~ " $user " ]]; then
           usermod usermod -s /sbin/nologin $user
           promptResult "禁用${username}用户登录"
        fi
    done
    echo -e "✅${color_green}禁用多余的账户sync halt shutdown成功!${color_no}"
}
#处理审计日志，使其满足6个月
function processAuditLog(){
    # 检查 auditd 是否安装
    if ! command -v auditctl &>/dev/null; then
        echo "❌ 审计服务 (auditd) ${color_red}未安装${color_no},审计日志设置失败,请先安装审计服务！"
    fi
    # 启用并启动 auditd
    systemctl enable auditd
    systemctl start auditd
    # 检测审计服务是否启动
    audit_status=$(systemctl is-active auditd)

    if [[ "$audit_status" != "active" ]]; then
        echo "❌审计服务(auditd)${color_red}未正确启动${color_no},请检查审计配置文件或者启动的内核参数是否限制了audit=0,更正后再运行此脚本！"
        return  255
    fi
    #审计
    AUDIT_CONF="/etc/audit/auditd.conf"
    # 备份配置文件
    cp -a "$AUDIT_CONF" "${AUDIT_CONF}.bak.$(date +%Y%m%d%H%M%S)"

    # 设置日志保留策略：保留至少6个月的数据（粗略估计日志大小）
    # 设置最大单个日志文件大小为 100MB
    sed -i 's/^max_log_file.*/max_log_file = 100/' "$AUDIT_CONF"
    # 设置最多轮转 180 份（按每天一份大概 6 个月）
    sed -i 's/^num_logs.*/num_logs = 180/' "$AUDIT_CONF"
    # 设置满了之后轮转
    sed -i 's/^max_log_file_action.*/max_log_file_action = ROTATE/' "$AUDIT_CONF"
    # 确保空间不足时不会删除日志
    if ! grep -q '^space_left_action' "$AUDIT_CONF"; then
        echo "space_left_action = SYSLOG" >> "$AUDIT_CONF"
    else
        sed -i 's/^space_left_action.*/space_left_action = SYSLOG/' "$AUDIT_CONF"
    fi
    # 确保admin_space_left_action也不是删除
    sed -i 's/^admin_space_left_action.*/admin_space_left_action = SUSPEND/' "$AUDIT_CONF"
    # 重启 auditd 服务使配置生效
    systemctl restart auditd
    echo "✅ 审计日志配置完成，当前策略支持日志保留6个月"
}
#serviceControl action[start|stop|restart] serviceName
function serviceControl() {
  if [ $# -eq 2 ]; then
    local serviceName=$2
    local action=$1
    if [ $(which service 2>/dev/null) ]; then
      systemctl "$action" "$serviceName"
    elif [ $(which service 2>/dev/null) ]; then
      service "$serviceName" "$action"
    else
      echo -e "$color_red未知系统无法执行serviceControl！！${color_no}"
      exit
    fi
  else
    echo -e "$color_red调用serviceControl参数错误${color_no}"
    exit
  fi
}
function stopAndDisableService()
{
	if systemctl is-active "$1" 2>/dev/null 1>/dev/null ;then 
		echo -e "${color_green}stop service $1 ...${color_no}"
		systemctl stop "$1"  1>/dev/null
		promptResult "stop service $1"
	fi;
	if systemctl is-enabled "$1" 2>/dev/null 1>/dev/null ;then
		echo -e "${color_green}disable service $1 ...${color_no}"
		systemctl disable "$1" 1>/dev/null
		promptResult "disable service $1"
	fi
}
function firewallTcpRejectPort()
{
    local firewall_type=$(detectFirewall)

    case "$firewall_type" in
        "firewalld")
            if firewall-cmd --permanent --direct --query-rule  ipv4 filter OUTPUT 0 -p tcp -m tcp --dport="$1" -j REJECT 1>/dev/null 2>/dev/null ;then
                echo -e "${color_green}TCP端口${2}已经关闭,无需处理${color_no}";
            else
                echo -e "${color_green}TCP关闭${2}端口...${color_no}"
                firewall-cmd --permanent --direct --add-rule ipv4 filter OUTPUT 0 -p tcp -m tcp --dport="$1" -j REJECT 1>/dev/null
                promptResult "TCP关闭${2}端口"
                firewall-cmd --reload
            fi
            ;;
        "ufw")
            if ufw status numbered | grep -q "DENY OUT.*$1/tcp" 1>/dev/null 2>/dev/null; then
                echo -e "${color_green}TCP端口${2}已经关闭,无需处理${color_no}";
            else
                echo -e "${color_green}TCP关闭${2}端口...${color_no}"
                ufw deny out "$1"/tcp 1>/dev/null 2>/dev/null
                promptResult "TCP关闭${2}端口"
            fi
            ;;
        *)
            echo -e "${color_red}系统没有使用firewall或UFW，关闭${2}TCP端口失败${color_no}"
            ;;
    esac
}
function firewallUdpRejectPort()
{
    local firewall_type=$(detectFirewall)

    case "$firewall_type" in
        "firewalld")
            if firewall-cmd --permanent --direct --query-rule  ipv4 filter OUTPUT 0 -p udp -m udp --dport="$1" -j REJECT 1>/dev/null 2>/dev/null ;then
                echo -e "${color_green}UDP端口${2}已经关闭,无需处理${color_no}";
            else
                echo -e "${color_green}UDP关闭${2}端口...${color_no}"
                firewall-cmd --permanent --direct --add-rule ipv4 filter OUTPUT 0 -p udp -m udp --dport="${1}" -j REJECT 1>/dev/null
                promptResult "UDP关闭${2}端口"
                firewall-cmd --reload
            fi
            ;;
        "ufw")
            if ufw status numbered | grep -q "DENY OUT.*$1/udp" 1>/dev/null 2>/dev/null; then
                echo -e "${color_green}UDP端口${2}已经关闭,无需处理${color_no}";
            else
                echo -e "${color_green}UDP关闭${2}端口...${color_no}"
                ufw deny out "$1"/udp 1>/dev/null 2>/dev/null
                promptResult "UDP关闭${2}端口"
            fi
            ;;
        *)
            echo -e "${color_red}系统没有使用firewall或UFW，关闭${2}UDP端口失败${color_no}"
            ;;
    esac
}

# 配置ICMP规则（禁用ping和traceroute）
function configureIcmpRules() {
    local firewall_type=$(detectFirewall)

    case "$firewall_type" in
        "firewalld")
            # 入站：屏蔽外来的 ICMP timestamp 请求（类型 13）
            firewall-cmd --permanent --direct --add-rule ipv4 filter INPUT 0 -p icmp -m icmp --icmp-type 13 -j DROP

            # 出站：依次屏蔽 echo-reply (0), destination-unreachable (3), time-exceeded (11), ICMP timestamp 回复 (14)
            for icmp_type in 0 3 11 14; do
                firewall-cmd --permanent --direct --add-rule ipv4 filter OUTPUT 0 -p icmp -m icmp --icmp-type "$icmp_type" -j DROP
            done
            firewall-cmd --reload
            ;;
        "ufw")
            # UFW处理ICMP规则
            # 拒绝ICMP timestamp请求（类型13）
            ufw deny in proto icmp from any to any port 13 1>/dev/null 2>/dev/null

            # 拒绝出站ICMP回复
            ufw deny out proto icmp to any 1>/dev/null 2>/dev/null

            # 注意：UFW的ICMP控制相对简单，复杂规则可能需要直接操作iptables
            echo "注意: UFW的ICMP规则相对简单，如需精确控制请考虑使用iptables"
            ;;
        *)
            echo -e "${color_red}系统没有使用firewall或UFW，ICMP规则配置失败${color_no}"
            ;;
    esac
}

function donetsecurity()
{
    echo -e "${color_green}关闭5353(dns)端口...${color_no}"
    stopAndDisableService "avahi-daemon"
    stopAndDisableService "cockpit"
    stopAndDisableService "cockpit.socket"
    promptResult "关闭5353(dns)端口"
    firewallTcpRejectPort 137 "137(netbios)"
    firewallUdpRejectPort 137 "137(netbios)"
    firewallTcpRejectPort 9090 "9090(cockpit)"
    firewallTcpRejectPort 53  "53(DNS)"
    firewallUdpRejectPort 53 "53(DNS)"
    firewallTcpRejectPort 33060 "33060(mysqlx)"
    firewallTcpRejectPort 135 "135(d1)"
    firewallUdpRejectPort 135 "135(d1)"
    firewallTcpRejectPort 138 "138(d2)"
    firewallUdpRejectPort 138 "138(d2)"
    firewallTcpRejectPort 139 "139(d3)"
    firewallUdpRejectPort 139 "139(d3)"
    firewallTcpRejectPort 445 "445(d4)"
    firewallUdpRejectPort 445 "445(d4)"
    #禁用 ping
    #禁用 traceroute (UDP 33434-33524)
    echo "关闭traceroute及ping功能"
    configureIcmpRules

    echo  -e "${color_green}关闭25(smtp)端口...${color_no}"
    stopAndDisableService "postfix"
    promptResult "关闭25(smtp)端口"
    #禁用远程桌面,如果3389端口是打开的首先提示
    RDP_PORT=3389
    LISTENING=$(ss -ltn | awk '{print $4}' | grep -w ":${RDP_PORT}$")
    if [ -n "$LISTENING" ]; then
        echo -e "${color_red}远程桌面服务${color_no}(${RDP_PORT}端口)已经开启,是否需要关闭?(y/n)"
        read   -a answer
        if [ "$answer" == "y" ]; then
            firewallTcpRejectPort ${RDP_PORT} "${RDP_PORT}(xrdp)"
            firewallUdpRejectPort ${RDP_PORT} "${RDP_PORT}(xrdp)"
        fi
    fi
}
runAsRoot
setupvars
donetsecurity
dosystemSettingSecurity
echo -e "${color_green}设置完毕，请重启系统使设置生效${color_no}"  
