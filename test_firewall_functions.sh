#!/bin/bash

# 测试脚本 - 验证防火墙函数的兼容性
# 这个脚本只测试函数逻辑，不会实际执行防火墙命令

# 导入原脚本中的颜色定义和函数
source netsecuritytools.sh

echo "=== 防火墙函数兼容性测试 ==="

# 测试detectFirewall函数
echo "1. 测试detectFirewall函数:"
firewall_type=$(detectFirewall)
echo "检测到的防火墙类型: $firewall_type"

# 模拟测试不同防火墙类型
echo -e "\n2. 模拟测试firewalld环境:"
# 临时修改detectFirewall函数返回值进行测试
function detectFirewall() { echo "firewalld"; }
echo "模拟firewalld环境下关闭端口3306:"
# firewallRemovePort 3306 "3306(mysql)"

echo -e "\n3. 模拟测试UFW环境:"
# 临时修改detectFirewall函数返回值进行测试
function detectFirewall() { echo "ufw"; }
echo "模拟UFW环境下关闭端口3306:"
# firewallRemovePort 3306 "3306(mysql)"

echo -e "\n4. 模拟测试无防火墙环境:"
function detectFirewall() { echo "none"; }
echo "模拟无防火墙环境下关闭端口3306:"
# firewallRemovePort 3306 "3306(mysql)"

echo -e "\n=== 测试完成 ==="
echo "修改后的函数支持以下防火墙:"
echo "- firewalld (原有支持)"
echo "- UFW (新增支持)"
echo "- 无防火墙时会显示错误信息"
